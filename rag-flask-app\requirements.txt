# requirements.txt - Dépendances Python pour l'application RAG

# Flask et extensions
Flask
Werkzeug

# Traitement des PDFs
PyPDF2
pdf2image

# Traitement des images
Pillow
opencv-python
pytesseract

# Traitement des vidéos
moviepy
opencv-python

# Reconnaissance vocale
SpeechRecognition
pydub

# Machine Learning et NLP
scikit-learn
numpy
scipy

# Utilitaires
uuid
requests

# Optionnel: pour de meilleures performances
# transformers==4.35.2  # Pour des embeddings plus avancés
# torch==2.1.1  # Si vous voulez utiliser des modèles de deep learning
