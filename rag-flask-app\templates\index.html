<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG System - Documents Intelligents</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <style>
        .glass-morphism {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .chat-bubble-user {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            border-radius: 18px 18px 4px 18px;
        }
        
        .chat-bubble-bot {
            background: linear-gradient(135deg, #e5e7eb, #f3f4f6);
            border-radius: 18px 18px 18px 4px;
            color: #1f2937;
        }
        
        .upload-area {
            transition: all 0.3s ease;
            border: 2px dashed #d1d5db;
        }
        
        .upload-area:hover, .upload-area.dragover {
            border-color: #4f46e5;
            background-color: rgba(79, 70, 229, 0.05);
        }
        
        .file-type-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-weight: bold;
            color: white;
        }
        
        .pdf-icon { background: #dc2626; }
        .image-icon { background: #059669; }
        .video-icon { background: #7c2d12; }
        
        .search-result {
            transition: transform 0.2s ease;
        }
        
        .search-result:hover {
            transform: translateY(-2px);
        }
        
        .typing-indicator {
            display: none;
        }
        
        .typing-indicator.show {
            display: block;
        }
        
        .typing-dots {
            display: inline-block;
            animation: typing 1.4s infinite;
        }
        
        @keyframes typing {
            0%, 60%, 100% { opacity: 0; }
            30% { opacity: 1; }
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .tab-button.active {
            background: #4f46e5;
            color: white;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Header -->
    <header class="glass-morphism text-white p-4 shadow-lg">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">🤖 RAG System</h1>
            <div class="flex items-center space-x-4">
                <span id="stats-display" class="text-sm"></span>
                <button onclick="refreshStats()" class="bg-white bg-opacity-20 px-4 py-2 rounded-lg hover:bg-opacity-30 transition">
                    🔄 Actualiser
                </button>
            </div>
        </div>
    </header>

    <div class="container mx-auto p-4 flex gap-4 h-screen">
        <!-- Sidebar -->
        <div class="w-1/4 space-y-4">
            <!-- Upload Section -->
            <div class="glass-morphism rounded-lg p-4 text-white">
                <h2 class="text-lg font-semibold mb-4">📁 Ajouter des Documents</h2>
                <div id="upload-area" class="upload-area p-6 rounded-lg text-center cursor-pointer">
                    <div class="text-4xl mb-2">📤</div>
                    <p class="text-sm">Glissez vos fichiers ici ou cliquez pour sélectionner</p>
                    <p class="text-xs mt-2 opacity-75">PDF, Images, Vidéos (max 100MB)</p>
                    <input type="file" id="file-input" class="hidden" multiple accept=".pdf,.png,.jpg,.jpeg,.gif,.bmp,.tiff,.mp4,.avi,.mov,.mkv,.webm,.flv">
                </div>
                <div id="upload-progress" class="mt-4 hidden">
                    <div class="bg-white bg-opacity-20 rounded-full h-2">
                        <div id="progress-bar" class="bg-green-400 h-2 rounded-full transition-all" style="width: 0%"></div>
                    </div>
                    <p id="upload-status" class="text-xs mt-1"></p>
                </div>
            </div>

            <!-- Documents List -->
            <div class="glass-morphism rounded-lg p-4 text-white flex-1 overflow-y-auto">
                <h2 class="text-lg font-semibold mb-4">📚 Documents</h2>
                <div id="documents-list" class="space-y-2">
                    <!-- Documents will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Tabs -->
            <div class="glass-morphism rounded-t-lg p-2 text-white">
                <div class="flex space-x-2">
                    <button class="tab-button active px-4 py-2 rounded-lg transition" data-tab="chat">
                        💬 Chat
                    </button>
                    <button class="tab-button px-4 py-2 rounded-lg transition" data-tab="search">
                        🔍 Recherche
                    </button>
                    <button class="tab-button px-4 py-2 rounded-lg transition" data-tab="documents">
                        📄 Gestion
                    </button>
                </div>
            </div>

            <!-- Chat Tab -->
            <div id="chat-tab" class="tab-content active glass-morphism rounded-b-lg rounded-tr-lg flex-1 flex flex-col">
                <div id="chat-messages" class="flex-1 p-4 overflow-y-auto space-y-4">
                    <div class="chat-bubble-bot p-4 max-w-3xl">
                        <p>👋 Bonjour! Je suis votre assistant RAG. Uploadez des documents et posez-moi des questions à leur sujet!</p>
                    </div>
                </div>
                
                <div class="typing-indicator p-4">
                    <div class="chat-bubble-bot p-4 max-w-3xl inline-block">
                        <span class="typing-dots">●</span>
                        <span class="typing-dots" style="animation-delay: 0.2s">●</span>
                        <span class="typing-dots" style="animation-delay: 0.4s">●</span>
                    </div>
                </div>

                <div class="p-4 border-t border-white border-opacity-20">
                    <div class="flex space-x-2">
                        <input 
                            type="text" 
                            id="chat-input" 
                            class="flex-1 px-4 py-3 rounded-lg bg-white bg-opacity-90 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Posez votre question sur les documents..."
                            onkeypress="handleChatKeyPress(event)"
                        >
                        <button 
                            onclick="sendMessage()" 
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition font-medium"
                        >
                            Envoyer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Search Tab -->
            <div id="search-tab" class="tab-content glass-morphism rounded-b-lg rounded-tr-lg flex-1 flex flex-col">
                <div class="p-4 border-b border-white border-opacity-20">
                    <div class="flex space-x-2">
                        <input 
                            type="text" 
                            id="search-input" 
                            class="flex-1 px-4 py-3 rounded-lg bg-white bg-opacity-90 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Rechercher dans tous les documents..."
                            onkeypress="handleSearchKeyPress(event)"
                        >
                        <button 
                            onclick="performSearch()" 
                            class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition font-medium"
                        >
                            🔍 Rechercher
                        </button>
                    </div>
                </div>
                
                <div id="search-results" class="flex-1 p-4 overflow-y-auto">
                    <div class="text-center text-white opacity-75 mt-20">
                        <div class="text-6xl mb-4">🔍</div>
                        <p>Effectuez une recherche pour voir les résultats</p>
                    </div>
                </div>
            </div>

            <!-- Documents Management Tab -->
            <div id="documents-tab" class="tab-content glass-morphism rounded-b-lg rounded-tr-lg flex-1 flex flex-col">
                <div class="p-4 border-b border-white border-opacity-20">
                    <h2 class="text-xl font-semibold text-white">Gestion des Documents</h2>
                </div>
                
                <div id="documents-management" class="flex-1 p-4 overflow-y-auto">
                    <!-- Documents management content -->
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed bottom-4 right-4 z-50 space-y-2">
        <!-- Toasts will appear here -->
    </div>

    <script>
        // Global variables
        let currentTab = 'chat';
        let uploadedFiles = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeTabs();
            initializeUpload();
            loadDocuments();
            refreshStats();
        });

        // Tab Management
        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.addEventListener('click', () => switchTab(button.dataset.tab));
            });
        }

        function switchTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Update tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}-tab`).classList.add('active');

            currentTab = tabName;

            // Load content for specific tabs
            if (tabName === 'documents') {
                loadDocumentsManagement();
            }
        }

        // Upload functionality
        function initializeUpload() {
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file-input');

            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            fileInput.addEventListener('change', handleFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            uploadFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            uploadFiles(files);
        }

        function uploadFiles(files) {
            const progressContainer = document.getElementById('upload-progress');
            const progressBar = document.getElementById('progress-bar');
            const statusText = document.getElementById('upload-status');
            
            progressContainer.classList.remove('hidden');
            
            files.forEach((file, index) => {
                const formData = new FormData();
                formData.append('file', file);
                
                statusText.textContent = `Upload ${index + 1}/${files.length}: ${file.name}`;
                
                fetch('/upload', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showToast('Erreur: ' + data.error, 'error');
                    } else {
                        showToast(`✅ ${file.name} uploadé avec succès!`, 'success');
                        loadDocuments(); // Refresh documents list
                        refreshStats();
                    }
                    
                    // Update progress
                    const progress = ((index + 1) / files.length) * 100;
                    progressBar.style.width = progress + '%';
                    
                    if (index === files.length - 1) {
                        setTimeout(() => {
                            progressContainer.classList.add('hidden');
                            progressBar.style.width = '0%';
                        }, 1000);
                    }
                })
                .catch(error => {
                    showToast('Erreur réseau: ' + error.message, 'error');
                });
            });
        }

        // Chat functionality
        function handleChatKeyPress(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addChatMessage(message, 'user');
            input.value = '';
            
            // Show typing indicator
            showTypingIndicator();
            
            // Send to server
            fetch('/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                hideTypingIndicator();
                if (data.error) {
                    addChatMessage('Erreur: ' + data.error, 'bot');
                } else {
                    addChatMessage(data.response, 'bot', data.sources);
                }
            })
            .catch(error => {
                hideTypingIndicator();
                addChatMessage('Erreur de connexion: ' + error.message, 'bot');
            });
        }

        function addChatMessage(message, sender, sources = null) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            
            if (sender === 'user') {
                messageDiv.className = 'chat-bubble-user p-4 max-w-3xl ml-auto text-white';
                messageDiv.textContent = message;
            } else {
                messageDiv.className = 'chat-bubble-bot p-4 max-w-3xl';
                messageDiv.innerHTML = `<p>${message}</p>`;
                
                // Add sources if available
                if (sources && sources.length > 0) {
                    const sourcesDiv = document.createElement('div');
                    sourcesDiv.className = 'mt-3 pt-3 border-t border-gray-300 text-xs';
                    sourcesDiv.innerHTML = '<strong>Sources:</strong> ' + 
                        sources.map(s => `${getFileTypeIcon(s.file_type)} ${s.filename}`).join(', ');
                    messageDiv.appendChild(sourcesDiv);
                }
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTypingIndicator() {
            document.querySelector('.typing-indicator').classList.add('show');
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTypingIndicator() {
            document.querySelector('.typing-indicator').classList.remove('show');
        }

        // Search functionality
        function handleSearchKeyPress(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        }

        function performSearch() {
            const input = document.getElementById('search-input');
            const query = input.value.trim();
            
            if (!query) return;
            
            const resultsContainer = document.getElementById('search-results');
            resultsContainer.innerHTML = '<div class="text-center text-white"><div class="animate-pulse">🔍 Recherche en cours...</div></div>';
            
            fetch('/search', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data);
            })
            .catch(error => {
                resultsContainer.innerHTML = `<div class="text-red-300">Erreur: ${error.message}</div>`;
            });
        }

        function displaySearchResults(data) {
            const resultsContainer = document.getElementById('search-results');
            
            if (!data.results || data.results.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="text-center text-white opacity-75">
                        <div class="text-4xl mb-4">🔍</div>
                        <p>Aucun résultat trouvé pour "${data.query}"</p>
                    </div>
                `;
                return;
            }
            
            const resultsHTML = data.results.map(result => `
                <div class="search-result bg-white bg-opacity-10 rounded-lg p-4 mb-4">
                    <div class="flex items-start justify-between mb-2">
                        <div class="flex items-center space-x-2">
                            ${getFileTypeIcon(result.file_type)}
                            <span class="text-white font-medium">${result.original_name}</span>
                        </div>
                        <span class="text-green-300 text-sm">Score: ${(result.score * 100).toFixed(1)}%</span>
                    </div>
                    <p class="text-gray-200 text-sm leading-relaxed">${result.content}</p>
                    <div class="mt-2 text-xs text-gray-400">
                        Ajouté le ${new Date(result.upload_date).toLocaleDateString('fr-FR')}
                    </div>
                </div>
            `).join('');
            
            resultsContainer.innerHTML = `
                <div class="mb-4 text-white">
                    <h3 class="text-lg font-semibold">${data.total_results} résultat(s) pour "${data.query}"</h3>
                </div>
                ${resultsHTML}
            `;
        }

        // Documents management
        function loadDocuments() {
            fetch('/documents')
            .then(response => response.json())
            .then(data => {
                const documentsList = document.getElementById('documents-list');
                
                if (!data.documents || data.documents.length === 0) {
                    documentsList.innerHTML = '<p class="text-gray-300 text-sm">Aucun document</p>';
                    return;
                }
                
                const documentsHTML = data.documents.map(doc => `
                    <div class="flex items-center justify-between bg-white bg-opacity-10 rounded-lg p-2">
                        <div class="flex items-center space-x-2">
                            ${getFileTypeIcon(doc.file_type)}
                            <span class="text-sm truncate" title="${doc.original_name}">${doc.original_name}</span>
                        </div>
                        <button onclick="deleteDocument('${doc.doc_id}')" class="text-red-400 hover:text-red-300 text-xs">
                            🗑️
                        </button>
                    </div>
                `).join('');
                
                documentsList.innerHTML = documentsHTML;
            });
        }

        function loadDocumentsManagement() {
            fetch('/documents')
            .then(response => response.json())
            .then(data => {
                const managementContainer = document.getElementById('documents-management');
                
                if (!data.documents || data.documents.length === 0) {
                    managementContainer.innerHTML = `
                        <div class="text-center text-white opacity-75 mt-20">
                            <div class="text-6xl mb-4">📄</div>
                            <p>Aucun document uploadé</p>
                        </div>
                    `;
                    return;
                }
                
                const documentsHTML = data.documents.map(doc => `
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 mb-4">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex items-center space-x-3">
                                ${getFileTypeIcon(doc.file_type)}
                                <div>
                                    <h3 class="text-white font-medium">${doc.original_name}</h3>
                                    <p class="text-gray-300 text-sm">Ajouté le ${new Date(doc.upload_date).toLocaleDateString('fr-FR')}</p>
                                </div>
                            </div>
                            <button onclick="deleteDocument('${doc.doc_id}')" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition">
                                Supprimer
                            </button>
                        </div>
                        
                        ${doc.processed_content && doc.processed_content.text ? `
                            <div class="mt-3">
                                <h4 class="text-gray-300 text-sm mb-2">Aperçu du contenu:</h4>
                                <p class="text-gray-200 text-sm bg-black bg-opacity-20 p-3 rounded">
                                    ${doc.processed_content.text.substring(0, 200)}...
                                </p>
                            </div>
                        ` : ''}
                        
                        <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                            <div class="text-gray-300">
                                <span class="font-medium">Type:</span> ${doc.file_type.toUpperCase()}
                            </div>
                            <div class="text-gray-300">
                                <span class="font-medium">ID:</span> ${doc.doc_id.substring(0, 8)}...
                            </div>
                        </div>
                    </div>
                `).join('');
                
                managementContainer.innerHTML = documentsHTML;
            });
        }

        function deleteDocument(docId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer ce document?')) {
                return;
            }
            
            fetch(`/delete/${docId}`, { method: 'DELETE' })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showToast('Erreur: ' + data.error, 'error');
                } else {
                    showToast('Document supprimé', 'success');
                    loadDocuments();
                    loadDocumentsManagement();
                    refreshStats();
                }
            })
            .catch(error => {
                showToast('Erreur: ' + error.message, 'error');
            });
        }

        // Statistics
        function refreshStats() {
            fetch('/stats')
            .then(response => response.json())
            .then(data => {
                const statsDisplay = document.getElementById('stats-display');
                statsDisplay.innerHTML = `
                    📊 ${data.total_documents} docs | 
                    📄 ${data.pdf_count} PDF | 
                    🖼️ ${data.image_count} images | 
                    🎥 ${data.video_count} vidéos
                `;
            });
        }

        // Utility functions
        function getFileTypeIcon(fileType) {
            const icons = {
                'pdf': '<div class="file-type-icon pdf-icon">PDF</div>',
                'image': '<div class="file-type-icon image-icon">IMG</div>',
                'video': '<div class="file-type-icon video-icon">VID</div>'
            };
            return icons[fileType] || '<div class="file-type-icon">?</div>';
        }

        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = type === 'error' ? 'bg-red-600' : type === 'success' ? 'bg-green-600' : 'bg-blue-600';
            
            toast.className = `${bgColor} text-white px-4 py-2 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            toastContainer.appendChild(toast);
            
            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            // Remove after 3 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toastContainer.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>