# search_engine.py - Moteur de recherche intelligent
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import pickle
import os
from typing import List, Dict, Any
import re
import unicodedata
import traceback

class SearchEngine:
    def __init__(self):
        self.vectorizer = TfidfVectorizer(
            max_features=10000,
            stop_words=None,
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.95
        )
        self.document_vectors = None
        self.documents = {}  # doc_id -> content
        self.index_file = 'database/search_index.pkl'
        print("--- Moteur de recherche initialisé. ---")
        self.load_index()
    
    def preprocess_text(self, text: str) -> str:
        if not text:
            return ""
        text = unicodedata.normalize('NFD', text)
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def index_document(self, doc_id: str, content: Dict[str, Any]):
        """Indexe un document pour la recherche"""
        try:
            # Toujours charger l'index le plus récent avant de modifier
            self.load_index()

            # Extraire et preprocesser le texte
            text = content.get('text', '')
            if not text:
                return
            
            processed_text = self.preprocess_text(text)
            self.documents[doc_id] = {
                'content': processed_text,
                'original': content,
                'type': content.get('type', 'unknown')
            }
            
            # Reconstruire l'index TF-IDF
            self.rebuild_index()
            self.save_index()
            
        except Exception as e:
            print(f"Erreur lors de l'indexation: {str(e)}")
    
    def rebuild_index(self):
        print("--- Reconstruction de l'index TF-IDF ---")
        if not self.documents:
            print("Aucun document à reconstruire. L'index sera vide.")
            return
        
        texts = [doc['content'] for doc in self.documents.values()]
        print(f"Nombre de documents à vectoriser: {len(texts)}")
        self.document_vectors = self.vectorizer.fit_transform(texts)
        print(f"Forme des nouveaux vecteurs de documents: {self.document_vectors.shape}")
    
    def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        print(f"--- Début de la recherche pour : '{query}' ---")
        try:
            self.load_index()
            
            if not self.documents or self.document_vectors is None:
                print("Arrêt de la recherche : aucun document ou vecteur dans l'index.")
                return []
            
            processed_query = self.preprocess_text(query)
            if not processed_query:
                print("Arrêt de la recherche : requête vide après pré-traitement.")
                return []
            print(f"Requête pré-traitée : '{processed_query}'")
            
            query_vector = self.vectorizer.transform([processed_query])
            print(f"Forme du vecteur de requête : {query_vector.shape}")
            print(f"Forme des vecteurs de documents : {self.document_vectors.shape}")

            similarities = cosine_similarity(query_vector, self.document_vectors).flatten()
            max_sim = max(similarities) if len(similarities) > 0 else 0
            print(f"Scores de similarité (max: {max_sim:.4f}): {similarities}")

            doc_ids = list(self.documents.keys())
            results = []
            for i, similarity in enumerate(similarities):
                if similarity > 0.01:
                    doc_id = doc_ids[i]
                    results.append({
                        'doc_id': doc_id,
                        'score': float(similarity),
                        'content': self.documents[doc_id]['original'].get('text', '')[:500],
                        'type': self.documents[doc_id]['type']
                    })
            
            print(f"Nombre de résultats après filtrage (seuil > 0.01) : {len(results)}")
            results.sort(key=lambda x: x['score'], reverse=True)
            
            print(f"--- Fin de la recherche. {len(results[:limit])} résultat(s) retourné(s). ---")
            return results[:limit]
        
        except Exception as e:
            print(f"ERREUR CRITIQUE lors de la recherche: {str(e)}")
            traceback.print_exc()
            return []
    
    def remove_document(self, doc_id: str):
        if doc_id in self.documents:
            del self.documents[doc_id]
            if self.documents:
                self.rebuild_index()
            else:
                self.document_vectors = None
            self.save_index()
    
    def save_index(self):
        print("--- Sauvegarde de l'index ---")
        try:
            index_data = {
                'vectorizer': self.vectorizer,
                'document_vectors': self.document_vectors,
                'documents': self.documents
            }
            with open(self.index_file, 'wb') as f:
                pickle.dump(index_data, f)
            print(f"Index sauvegardé avec succès dans '{self.index_file}'")
            print(f"Contenu sauvegardé: {list(index_data.keys())} avec {len(index_data.get('documents', {}))} documents.")
        except Exception as e:
            print(f"ERREUR CRITIQUE lors de la sauvegarde de l'index: {str(e)}")
            traceback.print_exc()
    
    def load_index(self):
        print("--- Tentative de chargement de l'index ---")
        try:
            if os.path.exists(self.index_file):
                print(f"Fichier d'index '{self.index_file}' trouvé. Chargement...")
                with open(self.index_file, 'rb') as f:
                    index_data = pickle.load(f)
                
                self.vectorizer = index_data.get('vectorizer', self.vectorizer)
                self.document_vectors = index_data.get('document_vectors')
                self.documents = index_data.get('documents', {})
                
                doc_count = len(self.documents)
                vec_status = "OK" if self.document_vectors is not None else "Vide"
                print(f"Chargement terminé. Documents: {doc_count}, Vecteurs: {vec_status}")
            else:
                print(f"Fichier d'index '{self.index_file}' non trouvé. L'index est vide.")
        except Exception as e:
            print(f"ERREUR CRITIQUE lors du chargement de l'index: {str(e)}")
            traceback.print_exc()
            # Réinitialiser en cas d'erreur de chargement pour éviter un état corrompu
            self.document_vectors = None
            self.documents = {}