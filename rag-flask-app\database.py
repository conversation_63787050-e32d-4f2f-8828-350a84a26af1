# database.py - Gestionnaire de base de données
import json
import os
import uuid
from typing import Dict, List, Any, Optional

class DatabaseManager:
    def __init__(self):
        self.db_file = 'database/documents.json'
        self.documents = self.load_database()
    
    def load_database(self) -> Dict[str, Dict]:
        """Charge la base de données depuis le fichier JSON"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"Erreur lors du chargement de la base: {str(e)}")
            return {}
    
    def save_database(self):
        """Sauvegarde la base de données dans le fichier JSON"""
        try:
            os.makedirs(os.path.dirname(self.db_file), exist_ok=True)
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.documents, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {str(e)}")
    
    def save_document(self, document_data: Dict[str, Any]) -> str:
        """Sauvegarde un nouveau document et retourne son ID"""
        doc_id = str(uuid.uuid4())
        self.documents[doc_id] = document_data
        self.save_database()
        return doc_id
    
    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """Récupère un document par son ID"""
        return self.documents.get(doc_id)
    
    def get_all_documents(self) -> List[Dict[str, Any]]:
        """Récupère tous les documents avec leurs IDs"""
        return [
            {**doc_data, 'doc_id': doc_id}
            for doc_id, doc_data in self.documents.items()
        ]
    
    def delete_document(self, doc_id: str) -> bool:
        """Supprime un document"""
        if doc_id in self.documents:
            del self.documents[doc_id]
            self.save_database()
            return True
        return False
    
    def update_document(self, doc_id: str, updates: Dict[str, Any]) -> bool:
        """Met à jour un document"""
        if doc_id in self.documents:
            self.documents[doc_id].update(updates)
            self.save_database()
            return True
        return False