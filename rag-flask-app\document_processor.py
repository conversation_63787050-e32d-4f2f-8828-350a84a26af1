# document_processor.py - Traitement des documents
import PyPDF2
from PIL import Image
import cv2
import numpy as np
from moviepy import VideoFileClip
import speech_recognition as sr
import pytesseract
import os
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class DocumentProcessor:
    def __init__(self):
        self.recognizer = sr.Recognizer()
    
    def process_pdf(self, filepath: str) -> Dict[str, Any]:
        """Traite un fichier PDF et extrait le texte et les images"""
        try:
            text_content = []
            images = []
            
            with open(filepath, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Extraire le texte de chaque page
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content.append({
                            'page': page_num + 1,
                            'text': page_text.strip()
                        })
            
            # Utiliser pdf2image pour extraire les images si nécessaire
            # (nécessite poppler-utils)
            try:
                import pdf2image
                pdf_images = pdf2image.convert_from_path(filepath)
                
                for i, img in enumerate(pdf_images):
                    # OCR sur l'image pour extraire plus de texte
                    ocr_text = pytesseract.image_to_string(img, lang='fra+eng')
                    if ocr_text.strip():
                        images.append({
                            'page': i + 1,
                            'ocr_text': ocr_text.strip()
                        })
            except ImportError:
                logger.warning("pdf2image non installé, extraction d'images limitée")
            
            return {
                'type': 'pdf',
                'text': ' '.join([page['text'] for page in text_content]),
                'pages': text_content,
                'images_ocr': images,
                'total_pages': len(pdf_reader.pages)
            }
        
        except Exception as e:
            logger.error(f"Erreur traitement PDF: {str(e)}")
            return {'type': 'pdf', 'text': '', 'error': str(e)}
    
    def process_image(self, filepath: str) -> Dict[str, Any]:
        """Traite une image et extrait le texte via OCR"""
        try:
            # Ouvrir l'image avec PIL
            image = Image.open(filepath)
            
            # OCR pour extraire le texte
            ocr_text = pytesseract.image_to_string(image, lang='fra+eng')
            
            # Analyse d'image basique avec OpenCV
            cv_image = cv2.imread(filepath)
            height, width = cv_image.shape[:2]
            
            # Détecter les objets/formes basiques
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            return {
                'type': 'image',
                'text': ocr_text.strip(),
                'dimensions': {'width': width, 'height': height},
                'format': image.format,
                'mode': image.mode,
                'objects_detected': len(contours),
                'file_size': os.path.getsize(filepath)
            }
        
        except Exception as e:
            logger.error(f"Erreur traitement image: {str(e)}")
            return {'type': 'image', 'text': '', 'error': str(e)}
    
    def process_video(self, filepath: str) -> Dict[str, Any]:
        """Traite une vidéo et extrait l'audio + frames clés"""
        try:
            # Charger la vidéo avec moviepy
            video = VideoFileClip(filepath)
            
            # Extraire l'audio
            audio_text = ""
            try:
                # Sauvegarder l'audio temporairement
                audio_path = filepath.replace('.', '_audio.wav')
                video.audio.write_audiofile(audio_path, verbose=False, logger=None)
                
                # Reconnaissance vocale
                with sr.AudioFile(audio_path) as source:
                    audio_data = self.recognizer.record(source)
                    try:
                        audio_text = self.recognizer.recognize_google(audio_data, language='fr-FR')
                    except sr.UnknownValueError:
                        try:
                            audio_text = self.recognizer.recognize_google(audio_data, language='en-US')
                        except sr.UnknownValueError:
                            audio_text = ""
                
                # Supprimer le fichier audio temporaire
                if os.path.exists(audio_path):
                    os.remove(audio_path)
            
            except Exception as e:
                logger.warning(f"Erreur extraction audio: {str(e)}")
                audio_text = ""
            
            # Extraire des frames clés et faire de l'OCR dessus
            frames_text = []
            duration = video.duration
            frame_times = [duration * i / 10 for i in range(10)]  # 10 frames réparties
            
            for i, time_point in enumerate(frame_times):
                try:
                    frame = video.get_frame(time_point)
                    frame_pil = Image.fromarray(frame.astype('uint8'))
                    
                    # OCR sur le frame
                    frame_ocr = pytesseract.image_to_string(frame_pil, lang='fra+eng')
                    if frame_ocr.strip():
                        frames_text.append({
                            'timestamp': time_point,
                            'text': frame_ocr.strip()
                        })
                except Exception as e:
                    logger.warning(f"Erreur traitement frame {i}: {str(e)}")
            
            video.close()
            
            # Combiner tout le texte extrait
            all_text = audio_text
            for frame in frames_text:
                all_text += " " + frame['text']
            
            return {
                'type': 'video',
                'text': all_text.strip(),
                'audio_transcript': audio_text,
                'frames_ocr': frames_text,
                'duration': duration,
                'fps': video.fps,
                'size': video.size,
                'file_size': os.path.getsize(filepath)
            }
        
        except Exception as e:
            logger.error(f"Erreur traitement vidéo: {str(e)}")
            return {'type': 'video', 'text': '', 'error': str(e)}
