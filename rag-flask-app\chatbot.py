# chatbot.py - Chatbot avec RAG
from typing import List, Dict, Any
import random

class ChatBot:
    def __init__(self):
        self.conversation_history = []
    
    def generate_response(self, message: str, search_results: List[Dict[str, Any]]) -> str:
        """Génère une réponse basée sur la requête et les résultats de recherche"""
        try:
            # Si aucun résultat pertinent, réponse générale
            if not search_results or max(r.get('score', 0) for r in search_results) < 0.1:
                return self._generate_fallback_response(message)
            
            # Construire le contexte à partir des résultats de recherche
            context_parts = []
            sources = []
            
            for result in search_results[:3]:  # Top 3 résultats
                if result.get('score', 0) > 0.1:
                    content = result.get('content', '')
                    if content:
                        context_parts.append(content[:300])  # Limiter la longueur
                        sources.append(result.get('type', 'document'))
            
            # Générer une réponse contextuelle
            response = self._generate_contextual_response(message, context_parts, sources)
            
            # Ajouter à l'historique
            self.conversation_history.append({
                'user': message,
                'bot': response,
                'sources': sources
            })
            
            return response
        
        except Exception as e:
            return f"Désolé, une erreur s'est produite lors de la génération de la réponse: {str(e)}"
    
    def _generate_contextual_response(self, message: str, context_parts: List[str], sources: List[str]) -> str:
        """Génère une réponse contextuelle basée sur les documents trouvés"""
        
        # Analyser le type de question
        message_lower = message.lower()
        
        # Question directe
        if any(word in message_lower for word in ['qu\'est-ce que', 'what is', 'définir', 'expliquer']):
            response = "D'après les documents analysés, "
        
        # Question de recherche
        elif any(word in message_lower for word in ['comment', 'pourquoi', 'how', 'why']):
            response = "Selon les informations trouvées, "
        
        # Question existentielle
        elif any(word in message_lower for word in ['y a-t-il', 'existe', 'is there', 'are there']):
            response = "Dans les documents consultés, "
        
        # Question générale
        else:
            response = "Voici ce que j'ai trouvé dans vos documents : "
        
        # Résumer les informations trouvées
        if context_parts:
            # Prendre des extraits pertinents
            relevant_info = []
            for context in context_parts:
                sentences = context.split('.')[:2]  # Premières phrases
                relevant_info.extend([s.strip() for s in sentences if s.strip()])
            
            if relevant_info:
                response += " ".join(relevant_info[:3])  # Max 3 phrases
            else:
                response += "Les documents contiennent des informations pertinentes, mais nécessitent une analyse plus approfondie."
        
        # Mentionner les sources
        if sources:
            unique_sources = list(set(sources))
            if len(unique_sources) == 1:
                response += f"\n\nSource: {unique_sources[0]}"
            else:
                response += f"\n\nSources: {', '.join(unique_sources)}"
        
        return response
    
    def _generate_fallback_response(self, message: str) -> str:
        """Génère une réponse par défaut quand aucun document pertinent n'est trouvé"""
        
        fallback_responses = [
            "Je n'ai pas trouvé d'informations spécifiques dans vos documents concernant cette question. Pourriez-vous reformuler ou être plus précis ?",
            "Aucun document dans ma base ne semble contenir d'informations directement liées à votre question. Avez-vous uploadé des documents sur ce sujet ?",
            "Je ne trouve pas d'éléments pertinents dans les documents analysés. Peut-être pourriez-vous ajouter plus de documents sur ce thème ?",
            "Désolé, je n'ai pas d'informations suffisantes dans les documents actuels pour répondre précisément à votre question."
        ]
        
        return random.choice(fallback_responses)
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Retourne l'historique de conversation"""
        return self.conversation_history[-10:]  # Derniers 10 échanges
    
    def clear_history(self):
        """Efface l'historique de conversation"""
        self.conversation_history = []
