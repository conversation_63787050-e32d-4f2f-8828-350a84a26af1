# app.py - Application Flask RAG principale
from flask import Flask, render_template, request, jsonify, send_file
from werkzeug.utils import secure_filename
import os
import logging
from datetime import datetime
import json
from document_processor import DocumentProcessor
from search_engine import SearchEngine
from chatbot import ChatBot
from database import DatabaseManager

# Configuration
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

# Créer les dossiers nécessaires
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static/uploads', exist_ok=True)
os.makedirs('database', exist_ok=True)

# Extensions de fichiers autorisées
ALLOWED_EXTENSIONS = {
    'pdf': ['pdf'],
    'image': ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'],
    'video': ['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv']
}

# Initialiser les composants
document_processor = DocumentProcessor()
search_engine = SearchEngine()
chatbot = ChatBot()
db_manager = DatabaseManager()

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def allowed_file(filename, file_type):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS.get(file_type, [])

def get_file_type(filename):
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    for file_type, extensions in ALLOWED_EXTENSIONS.items():
        if ext in extensions:
            return file_type
    return None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400
        
        # Déterminer le type de fichier
        file_type = get_file_type(file.filename)
        if not file_type:
            return jsonify({'error': 'Type de fichier non supporté'}), 400
        
        if file and allowed_file(file.filename, file_type):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = timestamp + filename
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Traiter le fichier selon son type
            logger.info(f"Traitement du fichier: {filename} (type: {file_type})")
            
            if file_type == 'pdf':
                result = document_processor.process_pdf(filepath)
            elif file_type == 'image':
                result = document_processor.process_image(filepath)
            elif file_type == 'video':
                result = document_processor.process_video(filepath)
            
            # Vérifier si l'extraction de texte a échoué
            if not result.get('text', '').strip():
                error_message = result.get('error', 'Aucun texte n a pu être extrait. Assurez-vous que le document contient du texte ou que les dépendances OCR (comme Tesseract) sont installées.')
                logger.error(f"Échec de l'extraction de texte pour {filename}: {error_message}")
                return jsonify({
                    'error': f"Le traitement du document a échoué. Raison probable : Tesseract OCR n'est pas installé ou le document ne contient pas de texte lisible. Erreur technique: {error_message}"
                }), 400
            
            # Sauvegarder dans la base de données
            doc_id = db_manager.save_document({
                'filename': filename,
                'original_name': file.filename,
                'file_type': file_type,
                'filepath': filepath,
                'processed_content': result,
                'upload_date': datetime.now().isoformat()
            })
            
            # Indexer pour la recherche
            search_engine.index_document(doc_id, result)
            
            return jsonify({
                'message': 'Fichier uploadé et traité avec succès',
                'doc_id': doc_id,
                'filename': filename,
                'type': file_type,
                'content_preview': result.get('text', '')[:200] + '...' if result.get('text') else ''
            })
        
        return jsonify({'error': 'Fichier non autorisé'}), 400
    
    except Exception as e:
        logger.error(f"Erreur lors de l'upload: {str(e)}")
        return jsonify({'error': f'Erreur lors du traitement: {str(e)}'}), 500

@app.route('/search', methods=['POST'])
def search():
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({'error': 'Requête vide'}), 400
        
        # Recherche dans les documents
        results = search_engine.search(query, limit=10)
        
        # Enrichir les résultats avec les métadonnées
        enriched_results = []
        for result in results:
            doc_data = db_manager.get_document(result['doc_id'])
            if doc_data:
                enriched_results.append({
                    'doc_id': result['doc_id'],
                    'score': result['score'],
                    'content': result['content'],
                    'filename': doc_data['filename'],
                    'original_name': doc_data['original_name'],
                    'file_type': doc_data['file_type'],
                    'upload_date': doc_data['upload_date']
                })
        
        return jsonify({
            'query': query,
            'results': enriched_results,
            'total_results': len(enriched_results)
        })
    
    except Exception as e:
        logger.error(f"Erreur lors de la recherche: {str(e)}")
        return jsonify({'error': f'Erreur lors de la recherche: {str(e)}'}), 500

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Message vide'}), 400
        
        # Rechercher des documents pertinents
        search_results = search_engine.search(message, limit=5)
        
        # Générer une réponse avec le contexte
        response = chatbot.generate_response(message, search_results)
        
        return jsonify({
            'message': message,
            'response': response,
            'sources': [
                {
                    'doc_id': r['doc_id'],
                    'filename': db_manager.get_document(r['doc_id'])['original_name'],
                    'file_type': db_manager.get_document(r['doc_id'])['file_type'],
                    'score': r['score']
                }
                for r in search_results[:3]  # Top 3 sources
            ]
        })
    
    except Exception as e:
        logger.error(f"Erreur lors du chat: {str(e)}")
        return jsonify({'error': f'Erreur lors de la génération de réponse: {str(e)}'}), 500

@app.route('/documents')
def list_documents():
    try:
        documents = db_manager.get_all_documents()
        return jsonify({
            'documents': documents,
            'total': len(documents)
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des documents: {str(e)}")
        return jsonify({'error': f'Erreur: {str(e)}'}), 500

@app.route('/document/<doc_id>')
def get_document(doc_id):
    try:
        document = db_manager.get_document(doc_id)
        if not document:
            return jsonify({'error': 'Document non trouvé'}), 404
        return jsonify(document)
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du document: {str(e)}")
        return jsonify({'error': f'Erreur: {str(e)}'}), 500

@app.route('/delete/<doc_id>', methods=['DELETE'])
def delete_document(doc_id):
    try:
        # Supprimer de la base de données
        document = db_manager.get_document(doc_id)
        if not document:
            return jsonify({'error': 'Document non trouvé'}), 404
        
        # Supprimer le fichier physique
        if os.path.exists(document['filepath']):
            os.remove(document['filepath'])
        
        # Supprimer de l'index de recherche
        search_engine.remove_document(doc_id)
        
        # Supprimer de la base de données
        db_manager.delete_document(doc_id)
        
        return jsonify({'message': 'Document supprimé avec succès'})
    
    except Exception as e:
        logger.error(f"Erreur lors de la suppression: {str(e)}")
        return jsonify({'error': f'Erreur lors de la suppression: {str(e)}'}), 500

@app.route('/stats')
def get_stats():
    try:
        documents = db_manager.get_all_documents()
        stats = {
            'total_documents': len(documents),
            'pdf_count': len([d for d in documents if d['file_type'] == 'pdf']),
            'image_count': len([d for d in documents if d['file_type'] == 'image']),
            'video_count': len([d for d in documents if d['file_type'] == 'video']),
            'total_size': sum(os.path.getsize(d['filepath']) for d in documents if os.path.exists(d['filepath']))
        }
        return jsonify(stats)
    except Exception as e:
        logger.error(f"Erreur lors du calcul des statistiques: {str(e)}")
        return jsonify({'error': f'Erreur: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)